{"id": "l2_transfer_funds_banking_system_v3", "version": "3.0", "pipeline": [{"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio_path": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "agent": "preprocessing_agent", "input": {"transcript": "text"}, "tools": {"external_tools": "openai"}, "output": {"emotion": "emotion", "intent": "intent", "gender": "gender", "latencyPreprocessing": "latencyPreprocessing", "clean_text": "clean_text"}}, {"step": "processing", "process": "transfer_funds_process", "agent": "processing_agent", "input": {"clean_text": "clean_text", "session_token": "session_token", "user_accounts": "user_accounts"}, "tools": {"external_tools": "openai", "DB_QUERY": "DB_QUERY"}, "output": {"sender_account": "sender_account", "receiver_account": "receiver_account", "amount": "amount", "currency": "currency", "confirmation_required": "confirmation_required", "llm_answer": "llm_answer", "latencyProcessing": "latencyProcessing"}}, {"step": "confirmation", "process": "transfer_confirmation_process", "agent": "processing_agent", "input": {"sender_account": "sender_account", "receiver_account": "receiver_account", "amount": "amount", "currency": "currency", "confirmation_required": "confirmation_required"}, "tools": {"external_tools": "openai"}, "output": {"confirmation_message": "confirmation_message", "user_confirmed": "user_confirmed", "latencyConfirmation": "latencyConfirmation"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "confirmation_message", "emotion": "emotion", "gender": "gender"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}, {"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio_path": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "confirmation_response", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "execution", "process": "transfer_execution_process", "agent": "processing_agent", "input": {"sender_account": "sender_account", "receiver_account": "receiver_account", "amount": "amount", "currency": "currency", "user_confirmed": "user_confirmed", "session_token": "session_token"}, "tools": {"external_tools": "TRANSACTION_API", "DB_QUERY": "DB_QUERY"}, "output": {"transaction_id": "transaction_id", "status": "status", "execution_message": "execution_message", "latencyExecution": "latencyExecution"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "execution_message", "emotion": "emotion", "gender": "gender"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "backChanneling": true, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 1, "fallback_state": "l2_error_handling_banking_system_v3"}}