{"id": "l2_greeting_banking_system_v3", "version": "3.0", "pipeline": [{"step": "llm", "process": "greeting_process", "agent": "processing_agent", "input": {"session_context": "session_context", "user_history": "user_history"}, "tools": {"external_tools": "openai"}, "output": {"greeting_text": "greeting_text", "personalized_message": "personalized_message", "session_tone": "session_tone", "latencyLLM": "latencyLLM"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "greeting_text", "emotion": "session_tone", "gender": "female"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "backChanneling": false, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "tts"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_generic"}}