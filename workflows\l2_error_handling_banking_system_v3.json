{"id": "l2_error_handling_banking_system_v3", "version": "3.0", "pipeline": [{"step": "llm", "process": "error_analysis_process", "agent": "processing_agent", "input": {"error_type": "error_type", "error_message": "error_message", "session_context": "session_context"}, "tools": {"external_tools": "openai"}, "output": {"error_response": "error_response", "retry_possible": "retry_possible", "escalation_needed": "escalation_needed", "latencyLLM": "latencyLLM"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "error_response", "emotion": "neutral", "gender": "female"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "backChanneling": false, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "tts"}, "onError": {"retry": 1, "fallback_state": "l2_fallback_generic"}}