{"workflow": {"id": "banking_customer_service_v3", "name": "Banking Customer Service Enhanced", "version": "3.0", "start": "Greeting", "allowed_actions": ["Transfer funds", "Check account balance", "Get exchange rates", "Multiple requests in same session", "User authentication", "Session management"], "prohibited_actions": ["Do not share PINs or passwords", "Do not process transactions without verification", "Do not store sensitive authentication data"], "engagement_messages": "Is there anything else I can help you with today?", "interrupt_config": {"global_settings": {"enabled": true, "vad_threshold": 0.05, "confirmation_window_seconds": 0.5, "min_interrupt_duration_seconds": 0.3, "tts_interrupt_cooldown_seconds": 0, "vad_method": "webrtcvad", "webrtc_aggressiveness": 3, "required_consecutive_frames": 5, "user_speech_end_silence_seconds": 1.5}}, "states": {"Greeting": {"id": "Greeting", "type": "input", "layer2_id": "l2_greeting_banking_system_v3", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "CollectIntent"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please let me finish my greeting, then I'll help you."}}, "CollectIntent": {"id": "CollectIntent", "type": "inform", "layer2_id": "l2_collect_intent_banking_system_v3", "expected_input": ["text"], "expected_output": ["audio_path", "latencyTTS", "intent", "confidence"], "transitions": [{"condition": "intent == 'transfer_funds'", "target": "TransferFunds"}, {"condition": "intent == 'check_balance'", "target": "CheckBalance"}, {"condition": "intent == 'exchange_rate'", "target": "ExchangeRate"}, {"condition": "intent == 'goodbye'", "target": "SessionEnd"}, {"condition": "confidence < 0.7", "target": "ClarifyIntent"}, {"condition": "intent == 'ambiguous'", "target": "ClarifyIntent"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please let me understand your request completely."}}, "ClarifyIntent": {"id": "ClarifyIntent", "type": "inform", "layer2_id": "l2_clarify_intent_banking_system_v3", "expected_input": ["text"], "expected_output": ["audio_path", "latencyTTS", "intent", "confidence"], "transitions": [{"condition": "intent == 'transfer_funds'", "target": "TransferFunds"}, {"condition": "intent == 'check_balance'", "target": "CheckBalance"}, {"condition": "intent == 'exchange_rate'", "target": "ExchangeRate"}, {"condition": "intent == 'goodbye'", "target": "SessionEnd"}, {"condition": "true", "target": "CollectIntent"}], "allowed_tools": ["STT", "LLM", "TTS"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Let me clarify what you need."}}, "AuthenticateUser": {"id": "AuthenticateUser", "type": "transaction", "layer2_id": "l2_authenticate_user_banking_system_v3", "expected_input": ["user_id", "auth_method"], "expected_output": ["audio_path", "latencyTTS", "auth_status", "session_token"], "transitions": [{"condition": "auth_status == 'success'", "target": "AuthSuccess"}, {"condition": "auth_status == 'failed'", "target": "AuthFailure"}, {"condition": "auth_status == 'timeout'", "target": "NoResponsePrompt"}], "allowed_tools": ["LLM", "TTS", "AUTH_API"], "interrupt_config": {"enabled": false, "reversible": false, "interrupt_message": "Authentication in progress. Please wait."}}, "AuthSuccess": {"id": "AuthSuccess", "type": "inform", "layer2_id": "l2_auth_success_banking_system_v3", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "AuthSuccessRedirect"}], "allowed_tools": ["LLM", "TTS"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Authentication successful."}}, "AuthFailure": {"id": "AuthFailure", "type": "inform", "layer2_id": "l2_auth_failure_banking_system_v3", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_tools": ["LLM", "TTS"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Authentication failed."}}, "AuthSuccessRedirect": {"id": "AuthSuccessRedirect", "type": "redirect", "expected_input": [], "expected_output": [], "transitions": [{"condition": "true", "target": "TransferFunds"}], "allowed_tools": [], "interrupt_config": {"enabled": false, "reversible": false, "interrupt_message": ""}}, "TransferFunds": {"id": "TransferFunds", "type": "transaction", "layer2_id": "l2_transfer_funds_banking_system_v3", "expected_input": ["sender_account", "receiver_account", "amount", "currency"], "expected_output": ["audio_path", "latencyTTS", "transaction_id", "status"], "transitions": [{"condition": "status == 'completed'", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"condition": "status == 'failed'", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"condition": "status == 'requires_auth'", "target": "AuthenticateUser"}], "allowed_tools": ["LLM", "TTS", "TRANSACTION_API", "DB_QUERY"], "interrupt_config": {"enabled": true, "reversible": false, "interrupt_message": "This transfer cannot be undone. Please let me finish before you speak."}}, "CheckBalance": {"id": "CheckBalance", "type": "inform", "layer2_id": "l2_check_balance_banking_system_v3", "expected_input": ["account_id"], "expected_output": ["audio_path", "latencyTTS", "balance", "currency"], "transitions": [{"condition": "true", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_tools": ["LLM", "TTS", "DB_QUERY"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Let me finish checking your balance."}}, "ExchangeRate": {"id": "ExchangeRate", "type": "inform", "layer2_id": "l2_exchange_rate_banking_system_v3", "expected_input": ["from_currency", "to_currency"], "expected_output": ["audio_path", "latencyTTS", "rate", "timestamp"], "transitions": [{"condition": "true", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_tools": ["LLM", "TTS", "EXCHANGE_API"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Let me get the current exchange rates for you."}}, "RepeatedUserQuery": {"id": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "inform", "layer2_id": "l2_repeated_user_query_banking_system_v3", "expected_input": [], "expected_output": ["audio_path", "latencyTTS", "continue_session"], "transitions": [{"condition": "continue_session == 'yes'", "target": "CollectIntent"}, {"condition": "continue_session == 'no'", "target": "SessionEnd"}, {"condition": "continue_session == 'timeout'", "target": "NoResponsePrompt"}], "allowed_tools": ["STT", "LLM", "TTS"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please let me understand your response."}}, "NoResponsePrompt": {"id": "NoResponsePrompt", "type": "inform", "layer2_id": "l2_no_response_prompt_banking_system_v3", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "SessionEnd"}], "allowed_tools": ["LLM", "TTS"], "interrupt_config": {"enabled": false, "reversible": false, "interrupt_message": "Are you still there?"}}, "ErrorHandling": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "inform", "layer2_id": "l2_error_handling_banking_system_v3", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_tools": ["LLM", "TTS"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Let me handle this error for you."}}, "SessionEnd": {"id": "SessionEnd", "type": "end", "layer2_id": "l2_session_end_banking_system_v3", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [], "allowed_tools": ["LLM", "TTS", "MEMORY_SAVE"], "interrupt_config": {"enabled": false, "reversible": false, "interrupt_message": "Thank you for using our service."}}}}}