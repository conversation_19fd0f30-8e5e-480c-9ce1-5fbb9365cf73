{"id": "l2_exchange_rate_banking_system_v3", "version": "3.0", "pipeline": [{"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio_path": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "agent": "preprocessing_agent", "input": {"transcript": "text"}, "tools": {"external_tools": "openai"}, "output": {"emotion": "emotion", "intent": "intent", "gender": "gender", "latencyPreprocessing": "latencyPreprocessing", "clean_text": "clean_text"}}, {"step": "processing", "process": "exchange_rate_process", "agent": "processing_agent", "input": {"clean_text": "clean_text"}, "tools": {"external_tools": "openai", "EXCHANGE_API": "EXCHANGE_API"}, "output": {"from_currency": "from_currency", "to_currency": "to_currency", "rate": "rate", "timestamp": "timestamp", "llm_answer": "llm_answer", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "llm_answer", "emotion": "emotion", "gender": "gender"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "backChanneling": true, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_error_handling_banking_system_v3"}}