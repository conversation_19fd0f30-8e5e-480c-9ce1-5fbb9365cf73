from agents.base.base_agent import BaseAgent
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
import asyncio
import os
import openai
import json
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from utils.audio_utils import synthesize_fallback_audio
import re

def extract_json_from_llm_output(output):
    # Remove markdown code block if present
    output = output.strip()
    if output.startswith('```'):
        output = re.sub(r'^```[a-zA-Z]*', '', output)
        output = output.strip('`\n ')
    # Try to find JSON substring
    match = re.search(r'\{.*\}', output, re.DOTALL)
    if match:
        return match.group(0)
    return output

class PreprocessingAgent(BaseAgent):
    def __init__(self, session_id=None, state_id=None):
        super().__init__("preprocessing_agent", session_id, state_id)
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        self.openai_client = openai.AsyncOpenAI(api_key=openai_api_key)

    def load_config(self):
        config_path = os.path.join(os.path.dirname(__file__), '../../..', 'config', 'state_manager_config.json')
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return {"enable_emotion": True, "enable_gender": True, "enable_disambiguation": False}

    async def process(self, input_data, context=None):
        start_time = asyncio.get_event_loop().time()
        context = context or {}
        self._log_process_start(input_data, context)
        session_id = self.session_id or context.get("session_id")
        redis_key = session_id
        config = self.load_config()
        try:
            # 1. Load the shared context dict
            shared_context = await self.load_context(redis_key) or {}
            fallback_result = await self.handle_redis_fallback(shared_context, session_id)
            if fallback_result:
                return fallback_result
            # 1a. Check for Redis fallback (e.g., tts_response)
            if isinstance(shared_context, dict) and "tts_response" in shared_context:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Redis unavailable. Fallback triggered.",
                    code=StatusCode.SERVICE_UNAVAILABLE,
                    outputs={"fallback_message": shared_context["tts_response"]},
                    meta={"agent": self.agent_name}
                )
            # 2. Get the text to preprocess
            text = shared_context.get("transcript")
            if not text:
                text = input_data.get("transcript")
            if not text:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No transcript found in shared context",
                    code=StatusCode.NOT_FOUND,
                    outputs={},
                    meta={"agent": self.agent_name}
                )
            # 3. Preprocess (clean, intent, emotion, gender)
            clean_text = await self.clean_text(text)
            intent_result = await self.classify_intent(clean_text)
            intent = intent_result["intent"]
            intent_confidence = intent_result["intent_confidence"]
            emotion_result = await self.detect_emotion(clean_text) if config.get("enable_emotion", True) else {"emotion": None, "emotion_confidence": 0.0}
            emotion = emotion_result["emotion"]
            emotion_confidence = emotion_result["emotion_confidence"]
            gender_result = await self.detect_gender(clean_text) if config.get("enable_gender", True) else {"gender": None, "gender_confidence": 0.0}
            gender = gender_result["gender"]
            gender_confidence = gender_result["gender_confidence"]
            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            outputs = {
                "intent": intent,
                "intent_confidence": intent_confidence,
                "clean_text": clean_text
            }
            if emotion is not None:
                outputs["emotion"] = emotion
                outputs["emotion_confidence"] = emotion_confidence
            if gender is not None:
                outputs["gender"] = gender
                outputs["gender_confidence"] = gender_confidence
            # Calculate overall preprocessing confidence value (average of available confidences)
            confidences = [intent_confidence]
            if emotion_confidence is not None:
                confidences.append(emotion_confidence)
            if gender_confidence is not None:
                confidences.append(gender_confidence)
            preprocessing_confidence_value = sum(confidences) / len(confidences) if confidences else 0.0
            outputs["preprocessing_confidence_value"] = preprocessing_confidence_value

            # Publish confidence to Redis for orchestrator
            try:
                import redis.asyncio as redis
                redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
                confidence_key = f"session:{session_id}:agent:{self.agent_name}:confidence"
                await redis_client.set(confidence_key, preprocessing_confidence_value, ex=3600)  # 1 hour expiry
                await redis_client.close()
            except Exception as e:
                self.logger.warning(f"Failed to publish confidence to Redis: {e}")
            # 4. Update and save the shared context, including latency
            # Always reload the latest context before saving
            latest_context = await self.load_context(redis_key) or {}
            latest_context.update(outputs)
            latest_context["latencyPreprocessing"] = duration_ms
            outputs["latencyPreprocessing"] = duration_ms
            await self.save_context(redis_key, latest_context)
            # 5. Publish notification
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent="Orchestrator",
                payload={"status": "complete"},
                context_keys_updated=list(outputs.keys()) + ["latencyPreprocessing"]
            )
            # await self.publish_notification("agent_completion", notification.to_json())
            # 6. Return result
            result = StateOutput(
                status=StatusType.SUCCESS,
                message="Text preprocessed successfully",
                code=StatusCode.OK,
                outputs=outputs,
                meta={
                    "agent": self.agent_name,
                    "original_length": len(text),
                    "cleaned_length": len(clean_text),
                    "latencyPreprocessing": duration_ms
                }
            )
            self._log_process_end(result, duration_ms)
            return result
        except Exception as e:
            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            self._log_error(e, input_data)
            # Notify orchestrator of error
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent="Orchestrator",
                payload={"status": "error", "error_message": str(e)},
                context_keys_updated=[]
            )
            # await self.publish_notification("agent_completion", notification.to_json())
            # Generate fallback audio
            fallback_text = "Sorry, something went wrong. Can you repeat that please?"
            audio_path = synthesize_fallback_audio(fallback_text, session_id=session_id or "fallback")
            outputs = {"fallback_message": fallback_text}
            if audio_path:
                outputs["audio_path"] = audio_path
            return StateOutput(
                status=StatusType.ERROR,
                message=f"PreprocessingAgent error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs=outputs,
                meta={
                    "agent": self.agent_name,
                    "latencyPreprocessing": duration_ms,
                    "error": str(e)
                }
            )

    # TODO Clean test using Disambiguation and Noise Filtering 
    async def clean_text(self, text: str) -> str:
        return " ".join(text.strip().lower().split())

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def classify_intent(self, text: str) -> dict:
        prompt = (
            "Given the following user message, identify the user's intent. Use these specific intent values:\n"
            "- account_balance: for checking account balance\n"
            "- fund_transfer: for transferring money\n"
            "- exchange_rate: for currency exchange\n"
            "- goodbye: for ending conversation\n"
            "- unknown: if none of the above\n\n"
            "Respond in JSON: {\"intent\": <intent>, \"confidence\": <float between 0 and 1>}\n"
            f"Message: {text}\nIntent:"
        )
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=50,
                temperature=0.2
            )
            import json
            raw = response.choices[0].message.content.strip()
            try:
                json_str = extract_json_from_llm_output(raw)
                result = json.loads(json_str)
                return {
                    "intent": result.get("intent", "unknown"),
                    "intent_confidence": float(result.get("confidence", 0.0))
                }
            except Exception:
                return {"intent": "unknown", "intent_confidence": 0.0}
        except Exception as e:
            self.logger.warning(f"Intent classification failed: {e}")
            return {"intent": "unknown", "intent_confidence": 0.0}

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def detect_emotion(self, text: str):
        prompt = (
            "What is the primary emotion expressed in the following text? Respond in JSON: {\"emotion\": <emotion>, \"confidence\": <float between 0 and 1>}\n"
            f"Text: {text}"
        )
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.3
            )
            import json
            raw = response.choices[0].message.content.strip()
            try:
                json_str = extract_json_from_llm_output(raw)
                result = json.loads(json_str)
                return {
                    "emotion": result.get("emotion", None),
                    "emotion_confidence": float(result.get("confidence", 0.0))
                }
            except Exception:
                return {"emotion": None, "emotion_confidence": 0.0}
        except Exception as e:
            self.logger.warning(f"Emotion detection failed: {e}")
            return {"emotion": None, "emotion_confidence": 0.0}

    # Note we should save the gender in memory so these function does not run every time user speaks
    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def detect_gender(self, text: str):
        prompt = (
            "Based on the following text, what is the likely gender of the speaker? Respond in JSON: {\"gender\": <male/female/unknown>, \"confidence\": <float between 0 and 1>}\n"
            f"Text: {text}"
        )
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.3
            )
            import json
            raw = response.choices[0].message.content.strip()
            try:
                json_str = extract_json_from_llm_output(raw)
                result = json.loads(json_str)
                return {
                    "gender": result.get("gender", None),
                    "gender_confidence": float(result.get("confidence", 0.0))
                }
            except Exception:
                return {"gender": None, "gender_confidence": 0.0}
        except Exception as e:
            self.logger.warning(f"Gender detection failed: {e}")
            return {"gender": None, "gender_confidence": 0.0} 