{"id": "l2_clarify_intent_banking_system_v3", "version": "3.0", "pipeline": [{"step": "llm", "process": "clarification_process", "agent": "processing_agent", "input": {"ambiguous_intent": "ambiguous_intent", "possible_intents": "possible_intents", "user_input": "user_input"}, "tools": {"external_tools": "openai"}, "output": {"clarification_question": "clarification_question", "expected_responses": "expected_responses", "latencyLLM": "latencyLLM"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "clarification_question", "emotion": "neutral", "gender": "female"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}, {"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio_path": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "processing", "process": "clarification_response_process", "agent": "processing_agent", "input": {"user_response": "text", "expected_responses": "expected_responses"}, "tools": {"external_tools": "openai"}, "output": {"intent": "intent", "confidence": "confidence", "llm_answer": "llm_answer", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "llm_answer", "emotion": "neutral", "gender": "female"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "backChanneling": true, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_generic"}}