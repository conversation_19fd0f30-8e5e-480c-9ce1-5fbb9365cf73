{"id": "l2_no_response_prompt_banking_system_v3", "version": "3.0", "pipeline": [{"step": "llm", "process": "timeout_process", "agent": "processing_agent", "input": {"session_context": "session_context", "timeout_duration": "timeout_duration"}, "tools": {"external_tools": "openai"}, "output": {"timeout_message": "timeout_message", "latencyLLM": "latencyLLM"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "timeout_message", "emotion": "neutral", "gender": "female"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "backChanneling": false, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "tts"}, "onError": {"retry": 1, "fallback_state": "l2_session_end_banking_system_v3"}}