{"id": "l2_authenticate_user_banking_system_v3", "version": "3.0", "pipeline": [{"step": "llm", "process": "auth_prompt_process", "agent": "processing_agent", "input": {"auth_method": "auth_method", "user_id": "user_id", "session_context": "session_context"}, "tools": {"external_tools": "openai"}, "output": {"auth_prompt": "auth_prompt", "auth_instructions": "auth_instructions", "latencyLLM": "latencyLLM"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "auth_prompt", "emotion": "neutral", "gender": "female"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}, {"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio_path": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "auth_verification", "process": "auth_verification_process", "agent": "auth_agent", "input": {"user_input": "text", "auth_method": "auth_method", "user_id": "user_id"}, "tools": {"external_tools": "AUTH_API"}, "output": {"auth_status": "auth_status", "session_token": "session_token", "auth_message": "auth_message", "latencyAuth": "latencyAuth"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "auth_message", "emotion": "neutral", "gender": "female"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "backChanneling": false, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 1, "fallback_state": "l2_auth_failure_banking_system_v3"}}