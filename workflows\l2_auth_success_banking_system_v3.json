{"id": "l2_auth_success_banking_system_v3", "version": "3.0", "pipeline": [{"step": "llm", "process": "auth_success_process", "agent": "processing_agent", "input": {"user_id": "user_id", "session_token": "session_token", "next_action": "next_action"}, "tools": {"external_tools": "openai"}, "output": {"success_message": "success_message", "next_instructions": "next_instructions", "latencyLLM": "latencyLLM"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "success_message", "emotion": "positive", "gender": "female"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "backChanneling": false, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "tts"}, "onError": {"retry": 1, "fallback_state": "l2_fallback_generic"}}