from typing import Dict, Any, Optional
import time
import asyncio
import sys
from pathlib import Path
from pydantic import ValidationError

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from core.logging.exceptions import WorkflowError, StateTransitionError
from core.logging.logger_config import get_module_logger
from core.memory.memory_manager import MemoryManager
from schemas.workflow_schema import Layer2
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.workflow_schema import State as StateConfig
from .layer2_pipeline import Layer2Pipeline
from asteval import Interpreter
from orchestrator.agent_registry import AgentRegistry, REGISTRY_REDIS_KEY
from schemas.agent_metadata import AgentMetadata

class State:
    """
    Represents a workflow state (Layer 1) that orchestrates execution and transitions.
    """
    def __init__(self, state_id: str, config: StateConfig, layer2_config: Layer2, memory: MemoryManager, tools_registry: AgentRegistry):
        self.id = state_id
        self.config = config
        self.layer2_config = layer2_config
        self.memory = memory
        self.tools_registry = tools_registry
        self.logger = get_module_logger(f"state_{state_id}", state_id=state_id)
        
    async def execute(self, input_data: dict, session_context: dict) -> StateOutput:
        """
        Executes the state by running its Layer 2 pipeline and evaluating transitions.
        
        Args:
            input_data (dict): Input data for the state (e.g., user input).
            session_context (dict): Session context (e.g., session_id, user_id).
        
        Returns:
            StateOutput: Standardized output with status, outputs, metrics, logs, and next state.
        """
        start_time = time.time()
        logs = []
        metrics = {"duration_ms": 0}
        outputs = {}
        status = StatusType.SUCCESS
        next_state_id = None

        try:
            # Validate input data against expected_input
            for key in self.config.expected_input:
                if key not in input_data:
                    raise WorkflowError(f"Missing expected input '{key}' for state '{self.id}'")

            self.logger.info("Executing state", action="execute", input_data={"input_data": input_data, "session_context": session_context}, layer="state_execution", session_id=session_context.get("session_id"), state_id=self.id)


            # Fetch additional context from memory
            memory_context = await self.memory.get_all_contextual()
            combined_context = {**session_context, **memory_context}
            
            # Initialize Layer2Pipeline
            pipeline = Layer2Pipeline(
                layer2_config=self.layer2_config,
                tools_registry=self.tools_registry,
                memory_interface=self.memory,
                state_id=self.id
            )
            
            # Execute pipeline
            pipeline_output = await pipeline.run(input_data, combined_context)
            logs.extend(pipeline_output.meta["logs"])

            outputs.update(pipeline_output.outputs)
            metrics.update(pipeline_output.meta["metrics"])
            if pipeline_output.status != StatusType.SUCCESS:
                status = pipeline_output.status

                self.logger.error("Pipeline execution failed", action="execute_pipeline", output_data={"pipeline_status": str(pipeline_output.status), "pipeline_message": pipeline_output.message}, reason=pipeline_output.message, layer="state_execution", state_id=self.id)
                raise WorkflowError(f"Pipeline failed: {pipeline_output.message}")
            # Validate outputs against expected_output
            for key in self.config.expected_output:
                if key not in outputs:
                    raise WorkflowError(f"Missing expected output '{key}' for state '{self.id}'")
            
            # Store outputs in memory if needed
            await self.memory.set("contextual", f"state_{self.id}_outputs", outputs)
        except WorkflowError as e:
            status = StatusType.ERROR
            self.logger.error("State execution failed", action="execute", reason=str(e), layer="state_execution", state_id=self.id)
            outputs["error"] = str(e)
            logs.append({"step": "state_execution", "status": "failure", "message": str(e)})
        except StateTransitionError as e:
            status = StatusType.ERROR

            self.logger.error("Transition evaluation failed", action="evaluate_transition", reason=str(e), layer="state_execution", state_id=self.id)
            outputs["error"] = str(e)
            logs.append({"step": "transition_evaluation", "status": "failure", "message": str(e)})
        except Exception as e:
            status = StatusType.ERROR
            self.logger.error("Unexpected error in state execution", action="execute", reason=str(e), layer="state_execution", state_id=self.id)

            outputs["error"] = f"Unexpected error: {str(e)}"
            logs.append({"step": "state_execution", "status": "failure", "message": str(e)})
        
        # Calculate total duration
        metrics["duration_ms"] = (time.time() - start_time) * 1000
        
        return StateOutput(
            status=status,
            message="State execution completed" if status == StatusType.SUCCESS else "State execution failed",
            code=StatusCode.OK if status == StatusType.SUCCESS else StatusCode.INTERNAL_ERROR,
            outputs=outputs,
            meta={"logs": logs, "metrics": metrics, "next": next_state_id}
        )

