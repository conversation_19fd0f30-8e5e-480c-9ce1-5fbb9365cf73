{"id": "l2_session_end_banking_system_v3", "version": "3.0", "pipeline": [{"step": "llm", "process": "session_summary_process", "agent": "processing_agent", "input": {"session_context": "session_context", "session_actions": "session_actions", "user_satisfaction": "user_satisfaction"}, "tools": {"external_tools": "openai"}, "output": {"session_summary": "session_summary", "goodbye_message": "goodbye_message", "latencyLLM": "latencyLLM"}}, {"step": "memory_save", "process": "persistence_process", "agent": "memory_agent", "input": {"session_summary": "session_summary", "session_data": "session_data"}, "tools": {"external_tools": "MEMORY_SAVE"}, "output": {"persistence_status": "persistence_status", "latencyMemory": "latencyMemory"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "goodbye_message", "emotion": "positive", "gender": "female"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "backChanneling": false, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "tts"}, "onError": {"retry": 1, "fallback_state": "l2_fallback_generic"}}