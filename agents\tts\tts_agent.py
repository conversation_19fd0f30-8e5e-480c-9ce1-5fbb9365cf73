from pathlib import Path
import sys
import os

project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from agents.base.base_agent import AudioAgent
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
import asyncio
from elevenlabs.client import Eleven<PERSON>abs
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from utils.audio_utils import synthesize_fallback_audio
import hashlib


os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "google_secret.json"


class TTSAgent(AudioAgent):
    def __init__(self, session_id=None, state_id=None):
        super().__init__(session_id, state_id)
        self.agent_name = "tts_agent"
        # self.elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")
        # if not self.elevenlabs_api_key:
        #     raise ValueError("ELEVENLABS_API_KEY environment variable is required")
        # self.client = ElevenLabs(api_key=self.elevenlabs_api_key)

    async def process(self, input_data, context=None):
        # Accepts either {"text": ...} or context with llm_answer
        text = input_data.get("text") if isinstance(input_data, dict) else None
        context = context or {}
        session_id = self.session_id or context.get("session_id")
        redis_key = session_id
        shared_context = await self.load_context(redis_key) or {}
        fallback_result = await self.handle_redis_fallback(shared_context, session_id)
        if fallback_result:
            return fallback_result
        if not text:
            text = shared_context.get("llm_answer")
        emotion = shared_context.get("emotion", "neutral")
        gender = shared_context.get("gender", "female")
        voice_config = {"emotion": emotion, "gender": gender}
        key = None
        if context and "key" in context:
            key = context["key"]
        
        return await self.text_to_speech(text, voice_config, key)

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def text_to_speech(self, text: str, voice_config: dict = None, key=None) -> StateOutput:
        start_time = asyncio.get_event_loop().time()
        self._log_process_start({"text": text, "voice_config": voice_config}, None)
        session_id = self.session_id
        redis_key = session_id
        try:
            if not text:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No text found for TTS",
                    code=StatusCode.NOT_FOUND,
                    outputs={},
                    meta={"agent": self.agent_name}
                )
            emotion = (voice_config or {}).get("emotion", "neutral")
            gender = (voice_config or {}).get("gender", "female")
            audio_path = await self.synthesize_speech_google(text, emotion, gender, session_id, key)
            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            outputs = {"audio_path": audio_path, "latencyTTS": duration_ms}
            # Update and save the shared context
            shared_context = await self.load_context(redis_key) or {}
            shared_context.update(outputs)
            await self.save_context(redis_key, shared_context)
            # Publish notification
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent="Orchestrator",
                payload={"status": "complete"},
                context_keys_updated=list(outputs.keys())
            )
            # await self.publish_notification("agent_completion", notification.to_json())
            result = StateOutput(
                status=StatusType.SUCCESS,
                message="TTS processed successfully",
                code=StatusCode.OK,
                outputs=outputs,
                meta={
                    "agent": self.agent_name,
                    "latencyTTS": duration_ms
                }
            )
            self._log_process_end(result, duration_ms)
            return result
        except Exception as e:
            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            self._log_error(e, {"text": text, "voice_config": voice_config})
            # Notify orchestrator of error
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent="Orchestrator",
                payload={"status": "error", "error_message": str(e)},
                context_keys_updated=[]
            )
            # await self.publish_notification("agent_completion", notification.to_json())
            # Generate fallback audio
            fallback_text = "Sorry, something went wrong. Can you repeat that please?"
            audio_path = synthesize_fallback_audio(fallback_text, session_id=session_id or "fallback")
            outputs = {"fallback_message": fallback_text}
            if audio_path:
                outputs["audio_path"] = audio_path
            return StateOutput(
                status=StatusType.ERROR,
                message=f"TTSAgent error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs=outputs,
                meta={
                    "agent": self.agent_name,
                    "latencyTTS": duration_ms,
                    "error": str(e)
                }
            )

    # to be adjusted later 
    def select_voice_id(self, gender):
        """
        Always returns a single default ElevenLabs voice_id, regardless of gender or emotion.
        """
        return "EXAVITQu4vr4xnSDxMaL"  # Example default voice (Rachel)

    def get_voice_settings(self, emotion):
        settings = {
            "stability": 0.6,
            "similarity_boost": 0.85,
            "style": 0.7,
            "use_speaker_boost": True
        }
        if emotion in ("excited", "happy", "joyful", "surprised"):
            settings["style"] = 0.95
        elif emotion in ("sad", "bored", "tired"):
            settings["style"] = 0.4
        elif emotion in ("angry", "frustrated"):
            settings["style"] = 0.8
        elif emotion == "neutral":
            settings["style"] = 0.7
        return settings

    async def synthesize_speech(self, text, emotion, gender, session_id):
        voice_id = self.select_voice_id(gender)
        settings = self.get_voice_settings(emotion)
        import os
        # Always save to project root
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
        audio_path = os.path.join(project_root, f"tts_output_{session_id}.mp3")
        def tts_sync():
            audio_gen = self.client.text_to_speech.convert(
                text=text,
                voice_id=voice_id,
                model_id="eleven_multilingual_v2",
                output_format="mp3_44100_128",
                voice_settings=settings
            )
            with open(audio_path, "wb") as f:
                for chunk in audio_gen:
                    f.write(chunk)
            return audio_path
        await asyncio.to_thread(tts_sync)
        return audio_path

    def generate_safe_filename(self, key: str) -> str:
        # Hash the key
        hashed_key = hashlib.sha256(key.encode()).hexdigest()
        return f"tts_output_{hashed_key}.mp3"

    async def synthesize_speech_google(self, text, emotion, gender, session_id, key=None):
        """Use Google Text-to-Speech API to synthesize speech."""
        from google.cloud import texttospeech
        import os
        
        # Create client
        client = texttospeech.TextToSpeechClient()
        
        # Set input text
        synthesis_input = texttospeech.SynthesisInput(text=text)
        
        # Select voice based on gender
        voice_gender = texttospeech.SsmlVoiceGender.FEMALE
        if (gender or "female").lower() == "male":
            voice_gender = texttospeech.SsmlVoiceGender.MALE
            
        # Configure voice
        voice = texttospeech.VoiceSelectionParams(
            language_code="en-US",
            ssml_gender=voice_gender
        )
        
        # Select audio format
        audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3
        )
        
        # Generate speech
        def tts_sync():
            response = client.synthesize_speech(
                input=synthesis_input, voice=voice, audio_config=audio_config
            )
            
            # Save to file
            project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
            audio_path = os.path.join(project_root, f"tts_output_{session_id}.mp3")
            if key:
                fileName = self.generate_safe_filename(key)
                audio_path = os.path.join(project_root,fileName)
            with open(audio_path, "wb") as out:
                out.write(response.audio_content)
                
            return audio_path
            
        return await asyncio.to_thread(tts_sync)
