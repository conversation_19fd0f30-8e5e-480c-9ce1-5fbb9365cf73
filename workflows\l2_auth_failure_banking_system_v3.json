{"id": "l2_auth_failure_banking_system_v3", "version": "3.0", "pipeline": [{"step": "llm", "process": "auth_failure_process", "agent": "processing_agent", "input": {"auth_method": "auth_method", "failure_reason": "failure_reason", "retry_count": "retry_count"}, "tools": {"external_tools": "openai"}, "output": {"failure_message": "failure_message", "retry_instructions": "retry_instructions", "latencyLLM": "latencyLLM"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "failure_message", "emotion": "neutral", "gender": "female"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "backChanneling": false, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "tts"}, "onError": {"retry": 1, "fallback_state": "l2_fallback_generic"}}