{"id": "l2_collect_intent_banking_system_v3", "version": "3.0", "pipeline": [{"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio_path": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "agent": "preprocessing_agent", "input": {"transcript": "text"}, "tools": {"external_tools": "openai"}, "output": {"emotion": "emotion", "intent": "intent", "confidence": "confidence", "gender": "gender", "latencyPreprocessing": "latencyPreprocessing", "clean_text": "clean_text"}}, {"step": "processing", "process": "intent_classification_process", "agent": "processing_agent", "input": {"clean_text": "clean_text", "intent": "intent", "confidence": "confidence"}, "tools": {"external_tools": "openai"}, "output": {"llm_answer": "llm_answer", "intent": "intent", "confidence": "confidence", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "llm_answer", "emotion": "emotion", "gender": "gender"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "backChanneling": true, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_generic"}}